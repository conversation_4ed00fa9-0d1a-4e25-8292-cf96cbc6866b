import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

interface WebsiteDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSignIn: () => void;
  animateFromSmall?: boolean;
}

const WebsiteDetailsModal: React.FC<WebsiteDetailsModalProps> = ({
  isOpen,
  onClose,
  onSignIn,
  animateFromSmall = false,
}) => {
  const navigate = useNavigate();
  const [isAnimating, setIsAnimating] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);
  const [animationPhase, setAnimationPhase] = useState<
    "initial" | "expanding" | "final"
  >("initial");

  // Handle animation states
  useEffect(() => {
    if (isOpen) {
      setShouldRender(true);
      setAnimationPhase("initial");

      if (animateFromSmall) {
        // Multi-phase animation from small modal with improved timing
        setTimeout(() => setAnimationPhase("expanding"), 80);
        setTimeout(() => setAnimationPhase("final"), 280);
        setTimeout(() => setIsAnimating(true), 350);
      } else {
        // Regular animation
        setTimeout(() => setIsAnimating(true), 10);
      }
    } else {
      setIsAnimating(false);
      setAnimationPhase("initial");
      // Wait for animation to complete before removing from DOM
      setTimeout(() => setShouldRender(false), 400);
    }
  }, [isOpen, animateFromSmall]);

  if (!shouldRender) return null;

  // Calculate animation styles based on phase and type
  const getModalStyles = () => {
    if (animateFromSmall) {
      switch (animationPhase) {
        case "initial":
          return "scale-[0.15] opacity-100 translate-x-[45vw] translate-y-[35vh] blur-sm";
        case "expanding":
          return "scale-[0.4] opacity-100 translate-x-[20vw] translate-y-[15vh] blur-[1px]";
        case "final":
          return isAnimating
            ? "scale-100 opacity-100 translate-x-0 translate-y-0 blur-0"
            : "scale-[0.4] opacity-100 translate-x-[20vw] translate-y-[15vh] blur-[1px]";
        default:
          return "scale-100 opacity-100 translate-x-0 translate-y-0 blur-0";
      }
    } else {
      return isAnimating
        ? "scale-100 opacity-100 translate-y-0 blur-0"
        : "scale-95 opacity-0 translate-y-4 blur-sm";
    }
  };

  const getBackgroundStyles = () => {
    if (animateFromSmall) {
      return animationPhase === "final" && isAnimating
        ? "bg-opacity-50"
        : "bg-opacity-0";
    }
    return isAnimating ? "bg-opacity-50" : "bg-opacity-0";
  };

  return (
    <div
      className={`fixed inset-0 bg-black flex items-center justify-center z-50 transition-all duration-600 ease-out ${getBackgroundStyles()}`}
    >
      <div
        className={`bg-white rounded-2xl p-8 max-w-4xl w-full mx-4 h-[80vh] relative flex flex-col transition-all duration-600 ease-out transform ${getModalStyles()}`}
        style={{
          transformOrigin: animateFromSmall ? "bottom right" : "center",
          transitionTimingFunction: animateFromSmall
            ? "cubic-bezier(0.175, 0.885, 0.32, 1.275)"
            : "ease-out",
          boxShadow:
            animateFromSmall && animationPhase === "final" && isAnimating
              ? "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)"
              : animateFromSmall
              ? "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
              : "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
        }}
      >
        {/* Animated Decorative Elements for Enhanced Effect */}
        {animateFromSmall && (
          <>
            <div
              className={`absolute -top-4 -right-4 w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full transition-all duration-700 ease-out ${
                animationPhase === "final" && isAnimating
                  ? "opacity-20 scale-100 animate-pulse"
                  : "opacity-0 scale-0"
              }`}
              style={{ transitionDelay: "400ms" }}
            />
            <div
              className={`absolute -bottom-4 -left-4 w-6 h-6 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full transition-all duration-700 ease-out ${
                animationPhase === "final" && isAnimating
                  ? "opacity-20 scale-100 animate-pulse"
                  : "opacity-0 scale-0"
              }`}
              style={{ transitionDelay: "500ms" }}
            />
            <div
              className={`absolute top-1/2 -left-2 w-4 h-4 bg-gradient-to-br from-blue-300 to-cyan-400 rounded-full transition-all duration-700 ease-out ${
                animationPhase === "final" && isAnimating
                  ? "opacity-15 scale-100 animate-pulse"
                  : "opacity-0 scale-0"
              }`}
              style={{ transitionDelay: "600ms" }}
            />
          </>
        )}

        {/* Close Button */}
        <button
          onClick={onClose}
          className={`absolute top-4 right-4 text-gray-500 hover:text-gray-700 text-2xl transition-all duration-300 ease-out transform hover:scale-110 hover:rotate-90 ${
            isAnimating
              ? "opacity-100 translate-x-0"
              : "opacity-0 translate-x-2"
          }`}
          style={{ transitionDelay: isAnimating ? "50ms" : "0ms" }}
          aria-label="Close modal"
        >
          ×
        </button>

        {/* Modal Content */}
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <div
            className={`text-center mb-6 transition-all duration-500 ease-out transform ${
              isAnimating
                ? "opacity-100 translate-y-0"
                : "opacity-0 translate-y-4"
            }`}
            style={{ transitionDelay: isAnimating ? "100ms" : "0ms" }}
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Welcome to Global Technology Interface
            </h2>
            <p className="text-gray-600">
              Your Gateway to Innovation and Technology
            </p>
          </div>

          {/* Features Grid */}
          <div className="flex-1 grid grid-cols-2 gap-4 mb-4">
            <div
              className={`bg-blue-50 p-4 rounded-xl transition-all duration-500 ease-out transform hover:scale-105 ${
                isAnimating
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-6"
              }`}
              style={{ transitionDelay: isAnimating ? "200ms" : "0ms" }}
            >
              <div className="text-blue-600 text-xl mb-2">🌐</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                Global Network
              </h3>
              <p className="text-sm text-gray-600">
                Connect with innovators and technology leaders worldwide.
              </p>
            </div>

            <div
              className={`bg-green-50 p-4 rounded-xl transition-all duration-500 ease-out transform hover:scale-105 ${
                isAnimating
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-6"
              }`}
              style={{ transitionDelay: isAnimating ? "300ms" : "0ms" }}
            >
              <div className="text-green-600 text-xl mb-2">🚀</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                Innovation Hub
              </h3>
              <p className="text-sm text-gray-600">
                Discover cutting-edge technologies and breakthrough innovations.
              </p>
            </div>

            <div
              className={`bg-purple-50 p-4 rounded-xl transition-all duration-500 ease-out transform hover:scale-105 ${
                isAnimating
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-6"
              }`}
              style={{ transitionDelay: isAnimating ? "400ms" : "0ms" }}
            >
              <div className="text-purple-600 text-xl mb-2">🤝</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                Partnership Opportunities
              </h3>
              <p className="text-sm text-gray-600">
                Find meaningful partnerships and collaboration opportunities.
              </p>
            </div>

            <div
              className={`bg-orange-50 p-4 rounded-xl transition-all duration-500 ease-out transform hover:scale-105 ${
                isAnimating
                  ? "opacity-100 translate-y-0"
                  : "opacity-0 translate-y-6"
              }`}
              style={{ transitionDelay: isAnimating ? "500ms" : "0ms" }}
            >
              <div className="text-orange-600 text-xl mb-2">📈</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                Market Access
              </h3>
              <p className="text-sm text-gray-600">
                Access global markets and showcase your technologies.
              </p>
            </div>
          </div>

          {/* Call to Action */}
          <div
            className={`text-center bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-xl transition-all duration-500 ease-out transform ${
              isAnimating
                ? "opacity-100 translate-y-0 scale-100"
                : "opacity-0 translate-y-4 scale-95"
            }`}
            style={{ transitionDelay: isAnimating ? "600ms" : "0ms" }}
          >
            <h3 className="text-lg font-semibold mb-2">
              Ready to Get Started?
            </h3>
            <p className="text-sm mb-3">
              Join thousands of innovators already part of our platform
            </p>
            <div className="flex justify-center space-x-3">
              <button
                onClick={() => {
                  onClose();
                  onSignIn();
                }}
                className="bg-white text-blue-600 px-4 py-2 rounded-lg text-sm font-semibold hover:bg-gray-100 transition-all duration-200 transform hover:scale-105"
              >
                Sign In
              </button>
              <button
                onClick={() => {
                  onClose();
                  navigate("/signup");
                }}
                className="bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-semibold hover:bg-blue-800 transition-all duration-200 transform hover:scale-105"
              >
                Get Started
              </button>
            </div>
          </div>

          {/* Platform Stats */}
          <div
            className={`grid grid-cols-3 gap-3 text-center mt-4 transition-all duration-500 ease-out transform ${
              isAnimating
                ? "opacity-100 translate-y-0"
                : "opacity-0 translate-y-4"
            }`}
            style={{ transitionDelay: isAnimating ? "700ms" : "0ms" }}
          >
            <div className="transition-all duration-300 hover:scale-110">
              <div className="text-xl font-bold text-blue-600">10K+</div>
              <div className="text-xs text-gray-600">Technologies</div>
            </div>
            <div className="transition-all duration-300 hover:scale-110">
              <div className="text-xl font-bold text-green-600">5K+</div>
              <div className="text-xs text-gray-600">Opportunities</div>
            </div>
            <div className="transition-all duration-300 hover:scale-110">
              <div className="text-xl font-bold text-purple-600">50+</div>
              <div className="text-xs text-gray-600">Countries</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WebsiteDetailsModal;
